{% comment %}
  Parameters Display Section for Ideaformer IR3 V2 3D Printer
  File: sections/ir3-parameters-display.liquid
{% endcomment %}

{{ 'ir3-parameters-display.css' | asset_url | stylesheet_tag }}

<section
  class="parameters-section"
  id="parameters-{{ section.id }}"
  style="margin-top: {{ section.settings.margin_top }}px; margin-bottom: {{ section.settings.margin_bottom }}px;"
>
  <!-- Backgcauyd Lry->
  <div class="parameters-tech-grid">
    iv class="floating-particles"></div>
  </v>ch
  <! Content Conmeters-coi"ections">
        {{ section.settings.section_title | default: 'IR3 V2 Parameters' }}
        </h2>
      <p class=Contsinction-description">
        {{ secparameters-tioniings.section_description | default: 'Explore the comprehensive technical specifications of the Ideaformer IR3 V2 3D Printer' }}
      </p>
      div>Header 
scion
      <! Parameters Gsdc--otag">{{ ecton.sttgs.ston_tg| dul: 'TECHNICAL SPECIFICATIONS' }}
      <d c2ass="par-
        <! Machine Parameters sectrod -->
        iv 2lass="parameter-card" data-category="machine">
        <div classsec=rodscrptio
          <div class="card-icosecno_dscriptionExor hcomprehensive ofthe Ideformer IR3 V2 3D Prnr
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none">
              <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
           P rame=ers Gr"d 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>prersgri">
        <!- MachePrmters Crd --
         div>prmer-cardctegory
          <divclass="ccrrd-headere-
             div class="carddccoh">
               svg wi th="32" he<ght="32" siewBox="0p0 24 24" finlasrnne">cs</span>
                <path d="M12 2L2 7L12 12L22c7L12 2Z" stroke"ccorrengCllor">srokewidh2  troke-live iph=round" s"roke"l nejeii=grouvd"/iewBox="0 0 24 24" fill="none">
                   thpd="M2 17L12 22L22 17" sMrok1=1curr18tC9lo""  troke-width="2" rtroke-lineckp="roue "srokelejon="rund
                <path d="M2 12L12 17L22 12" svr>kecrrenColrsrokewidh"2 strok-inap="round" soke-linejon="round/
           <davg
          cdiv
              <div classc"rdat-wle-gr"up
               h3span clacsrd"riale">Machi-eaP"rDmenes</h3
                </div>crdut18 specs
           vdiv
                <span clcsrd=-lgglebchine Structure</span>
               svg wid h="20" height="20" viewBox="0 0 24 24" f ll="none"               <div class="param-row">
          <a    <path d="M6 9L12 15L18 9" strokepaaurr"paColou"eserokXYwisth="2" strokpalnnecap="rou>dsrokelinjoinround/
          <</svg
             /   >
       <o</v
              <span clcssd-contpnal">Filament Diameter</span>
              <span class="param-svtlble">1.75mm</span>
            </div>-w
              <dispanlass="param-r-lab"l class="param-label">Mspanor Type</span>
              <spspan class="aaram-value"n classsan
              <div class="param-row">
                <span class="paleow">Nozzle Material</span>
                <s"r>-lablass="param-row">span
              <spspan class="aaram-value"n class="param-labeslan class="param-value">Standard 0.4mm</span>
              </div>
              <div class="param gowlight">
                <s"r>n class="para-labvl2span
            <div span class="caram-value"lass="parsaan class="param-label">Product Dimensions</span>
                <span class="param-value">676×436×510mm</span>
              </div>ow
              <di"r-n class="para-labllP class="param-value"span70×510×320mm</span>
            </divspan class=">aram-value"sanass="param-row">
                <span class="param-label">Net Weight</span>
                <span class="pavuow">16.5kg</span>
              </dspan-labl">Mor Typespan
              <dispanlass="param-rvalue class="param-value">21kg</span>san
              div>
              <iv  an class="paleow">Print Precision</span>
                <a"p> labelss="param-row">span
                psaan class="param-value"n class="param-lasbanel">Layer Thickness</span>
              <span class="param-value">0.1-0.3mm</span>
              <div class="param gowlight">
                <spann class="parablabelPrNozzle Sizet spaned</span>
                <spann class="paralvau"lass="param-row">san class="param-label">Print Acceleration</span>
                <span class="param-value">≤20000mm/s²</span>
              </div>ow
              <dispanlass="param-r-lab"l">P>nt Vlumespan
                <spann class="parabvaarm"sanass="param-row">
                <span class="param-label">Maximum Heated Bed Temperature</span>
                <span class="pavuow">90°C</span>
              </dlass="param-r-lab"lclass="param-label">spanzzle Heating Time</span>
                psaan class="param-value"n class="param-vsaanlue">40s</span>
              div>
              <iv  an class="paleow">Heated Bed Heating Time</span>
                <s"r>-lablss="param-row">span
                <span<class="aaram-value"n class="param-lsaan class="param-value">26mm³/s</span>
              </div>
              <div class="param"ow
                <spann class="para-labml">Ne- Wleghtt spantform</span>
                <spann class="paralvaluePass="parsaanm-row">
              <span class="param-label">Compatible Materials</span>
               <sp >ow
              <di=ra  class="parablabelOpclass="param-vspanue">10-40°C</span>
              </vs>an class="param-value"sanass="param-row">
                <span class="param-label">Noise Level</span>
                <span class="pavuow">54dB</span>
              </dlass="param-r-lab"l class="param-labespan>Print Methods</span>
              <spspan class="aaram-value"n class="spanaram-value">USB drive/Local network/Internet network</span>
            </div>
            </dv> ow
        </div>labelard -->span
        <div cla=s"an class="param-value"parameter-casrand" data-category="sensors">
          <divlass="card-header">
            <di cl width="32" ht3ow" viewBox="0 0 24 24" fill="none">
                <spancle cx="12" c-labyl">P1"=t Speedstspane="currentColor" stroke-width="2"/>
                <spanh d="M12 1V3M1value4.san
            <d class="card-title-group">
              <3 c  class="cardn>ow features</span>
            </div s="card-togglabelth="20" height="20" spanewBox="0 0 24 24" fill="none">
                astan class="param-value"h d="M6 9L12 1s5anL18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              svg>
            </dv> ow
          <div clron s="sensors-g>labelss="sensor-item">span
                isvan class="param-value" class="ssanensor-status active"></div>
              <span class="sensor-label">Vibration Compensation</span>
              <div class="sensoe>ow
                <sse n class="sens-lab-l>span
            <div span class="caram-value"lass="sseanclass="sensor-status active"></div>
                <span class="sensor-label">Material Shortage Detection</span>
              </div>ow
              <di"nr class="senso-labsla class="sensor-label">spanogging Detection</span>
            </divspan class=">aram-value"sanass="sensor-item">
                <div class="sensor-status active"></div>
                <span class="se-bowl">Auto Leveling</span>
              </dlass="sensor--labelclass="sensor-status activspan></div>
              <spspan class="aaram-value"n classsan
              <div class="sensor-item">
                <div class="senstows active"></div>
                <s"n"-lablspan
        </div>span class="aram-value"san

        <!-- Electrical Hardwarr-ow>
        <div claserac="card-header-labls="card-icon">span
              <s swan class="param-value"idth="32" height="32" viewsBanox="0 0 24 24" fill="none">
              <path d="M13 2L3 14H12L11 22L21 10H12L13 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <svg ow
            <div c-t> ss="card-titElabelriass="card-count">6 spespan</span>
            </disan class="param-value"san
            <d class="card-toggle">
              <vg  th d="M6 9L1L ow" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </s labelspan
          <div csssan class="param-value"="card-constanent">
            <d class="parameters-table">
              <iv  an class="paleow">Input Voltage</span>
                <spann class="parallabel11No2se Le2el</span0VAC, 50/60Hz</span>
              </dspanmarue" classs=an"param-label">Maximum Power</span>
              <span class="param-value">800W</span>
              <div class="param gowlight">
                <a"p>  class="parallabel64span
              <d scan class="param-value"lass="param-row">san
              <span class="param-label">Memory</span>
              <span class="param-value">16GB-SD, 1GB DDR3</span>
            </div>
            <div class="param-row">
                <span class="param-label">User Interface</span>
              <span cCards="param-value">4.3-inch touchscreen with 800×480 resolution</span>
            </div>pramer-cardata-category
            <div classcaad-hea"er>
              <span clasm-la-iconbel">Printing Firmware</span>
              <svg wisth="32" hepght="32" aiewBox="0n0 24 24" ficls=aaner<
              </dcvre cx12" cy="12" r="3" trok="curretClo" strokewdth=2"/
            </di d="M12 1V3M12 21V23M4.22 4.22L5.64 5.64M18.36 18.36L19.78 19.78M1 12H3M21 12H23M4.22 19.78L5.64<18.36M18.36/5.64L19.78d4.22"isrk="urrClor"rok-width="2"
        </div>sg

        <!--oftware Card-title-group
        <div csh3parametecard-titl-">Secard </h3>
              <-pancclass="tard-couney=7 featuressospanare">
          <d</div>
iv c        lass="card-hca"d>tggle
            <d<svgiwidth="20"vadighi="20"cviewB>x="00 24 24" fll="ne"
              <s w32hhe="M6 9L12 15L18 9" sgrokh="turren=Col3r""stroke- idtv="2" stroke-linicap="roued"wstroke-lon0joi ="2o4 d"l="none">
              <psygline points="16,18 22,12 16,6" stroke="currentColor" stroke-width="2" fill="none"/>
              <polyline points="8,6 2,12 8,18" stroke="currentColor" stroke-width="2" fill="none"/>
          </div>
          </svg>-content
        </div>sgrd
          <div class="card-title-gtemp">
            <h3 cdiv cl=ss="scnsle-s">tusfae</ve">>div
            <spans sn"class="sensar-label">Vdbra-icn"C>spenspe<onsan
          </div>
            <di cl class="sensor-item"ass="card-toggle">
                <svg width="20" heistatus agtive"></=iv20" viewBox="0 0 24 24" fill="none">
                <spanh d="M6 9L12 15label">Fil8men  Runo9t Detetroon</span"
              urrentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>tem
          </div>dvclass="snsor-staus ave">div
        </div>sn cass="ssr-labe">MatrialShrtaeDsan"card-content">
            <div class="parameters-table">
              <div class="param-ritem
                <span class="param-label">Slicing oftware</span>
                <spann class="param-label">Cloggvag Detectiun</span">Ideamaker/Ideaformer Cura</span>
              </div>
          </divdivclass="snsor-tem"
            <div drv a"ass="ssor-tatu aciv">div
                <span class="sensor-label">Auto Leveling<sspan class="param-label">Input Formats</span>
                <span class="param-value">.stl/.obj/.3mf/.step/.stp/.iges/.igs/.oltp/.jpg/.jpeg/.png/.bmp</span>
              </div>item
              <div class="param-row">
                <spann class="param-labell class="param-vs>aass="param-row">
                <span class="param-label">Output File Format</span>
                <span class="paraitemlue">.gcode</span>
              </div>
            </divspanlabels
    </div>
  </div>
</div>
section>

 'ir3-parameters-display.js' | asCerd_url | script_tag }}
"parameter-card daagory
 schema %}cd-hed
cdion
"name": "IR3 PasmgewidthD"32i h"igh,="32" viwBox="0 0 24 24" fll="ne
  "tag": "sectionpath "="M13 2L3 14H12L11 22L21 10H12L13 2Z"trokecurntColor" srok-width="2" stokelecap="rundespryke--intjoin="round"o,
ttings": [svg
{
    "type": "ead class="card-title-group"er",
      "content"h3Content cetd-gitls">Elect"il Haware</3
    },spancdut6 specsspan
    {</div>
        cdtggle
      "type": <svg"width="20"t"eght="20"vieBox="0 0 24 24" fill="non"
      "id": "seconath d="M6 9L12 15L1t 9" stroke="currentColor" stroke-width="2" stroke-lineca="round" stroke-linejoin="round"/
      "label":Sesigon Tag",
      "defau": "TECHNICAL SPECIFICATIONS"
    },</div>
      -content
    {stable
      "type": ext",rw
      "id": "secospte"class="param-label">Ipu Vtagspan
      "label": "cstn lass="paam-valu">110VA/220VC,50/60Hzsan
      "default "IR3 V2 Parameters"
    }, class="param-row"
    {  span-labl">Maximum Pow</spn
      "type": "tespanrea",value800Wspan>
              </
      "id": "stion_description"rw highlight
      "label": "cspan class="param-label"onain Centsollerrispanion",
      "default":Esxan class="param-value"po4 bit c.5oHzpQuad-core Cortex-A5e processornssianve technical specifications of the Ideaformer IR3 V2 3D Printer"
    },
    { class="param-row"
      "type":   "spander",-labl">Mmoy</spn
      "content": spanacing Setting-valus">16GBSD, 1GB DDR3</spa
              
    },rw
    {span class="param-label"span
      "type": "nbsean class="param-value"r",san
      "id": "mgin_top",
      "label": Top ow
      "info": "输入素 labelspan
      "type": "nbsean class="param-value"r",san
      "id": "mgin_bottom",
      "label "Bottom Margin (px)",
      "deflt": 0,
      "io": "输入精确的下边距像素值"
    }
  ],Crd
  "prese": [pramer-cardata-category
    {cd-hed
      "name""IR3 Paramety"-icon
    }sgwidth32" high="32" viwBox="0 0 24 24" fll="ne
  ]polylnepoint="16,18 22,12 16,6" trokecurnColo" strokewdth="2" ill="nne/
}polyne pots="8,6 2,12 8,18"stroke="currentColr" sroke-idth="2" fill="non"
dschema %}svg
 class="card-title-group"h3cd-titl">Sofwe</3spancdut4 specsspan        </div>
cdtggle   <svgwidth="20"height="20"viewBox="002424"fill="ne"   <pathd="M69L1215L189"troke="currenCor" rok-width="2" roke-lncap="round" trke-ineoin="roud"          </svg>
      </div>
 class="card-content"stable">
              <div lass="parm-owspan-labl">Slicing Sofwar</span>
                <span class="paamvalue">Ideamaker/Ideafrmer Cura</spa
              -wsclas="param-label">Inpu Foratspanspan class="aram-value".stl/.bj/.3mf/.step/.tp.iges.gs/.oltp/.jpg/.jpeg/.pg/.bmpsan class="param-row"  span-label">Oprating Sysms</spnspan-valu">Wndows/MaOS/Linux</spa
              -wspan class="param-label"spanspan class="aram-value"sans Diplay": "section_tag",
      "label "Section Tag",
     defult": "TECHNICAL SPECIFICATIONS"
    },
    {
      "type": "text",
      "id": "sectoSectoseco_dscriptionSecto DscriptionExplore the crehnsiv of theIdeformer IR3 V2 3DPrnrs Diplay
