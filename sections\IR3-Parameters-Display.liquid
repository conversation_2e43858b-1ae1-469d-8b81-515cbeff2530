{% comment %}
  IR3 Parameters Display Section
  File: sections/IR3-Parameters-Display.liquid
{% endcomment %}

{{ 'IR3-Parameters-Display.css' | asset_url | stylesheet_tag }}

<section
  class="parameters-section"
  id="parameters-{{ section.id }}"
  style="margin-top: {{ section.settings.margin_top }}px; margin-bottom: {{ section.settings.margin_bottom }}px;"
>
  <!-- Background Layer -->
  <div class="parameters-background">
    <div class="gradient-overlay"></div>
    <div class="tech-grid"></div>
    <div class="floating-particles"></div>
  </div>

  <!-- Content Container -->
  <div class="parameters-container">
    <div class="container">
      <!-- Header Section -->
      <div class="parameters-header">
        <div class="header-content">
          <span class="section-badge">Technical Specifications</span>
          <h2 class="section-title">{{ section.settings.title | default: 'IR3 V2 Parameters' }}</h2>
          <p class="section-description">{{ section.settings.description | default: 'Comprehensive technical specifications and features overview' }}</p>
        </div>
      </div>

      <!-- Category Navigation -->
      <div class="category-nav">
        <button class="category-btn active" data-category="machine">
          <span class="btn-icon">⚙️</span>
          <span class="btn-text">Machine Parameters</span>
        </button>
        <button class="category-btn" data-category="sensors">
          <span class="btn-icon">📡</span>
          <span class="btn-text">Sensors</span>
        </button>
        <button class="category-btn" data-category="electrical">
          <span class="btn-icon">⚡</span>
          <span class="btn-text">Electrical Hardware</span>
        </button>
        <button class="category-btn" data-category="software">
          <span class="btn-icon">💻</span>
          <span class="btn-text">Software</span>
        </button>
      </div>

      <!-- Parameters Content -->
      <div class="parameters-content">
        <!-- Machine Parameters -->
        <div class="parameter-category active" data-category="machine">
          <div class="parameter-grid">
            <div class="parameter-card">
              <div class="param-label">Printing Technology</div>
              <div class="param-value">FDM</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Machine Structure</div>
              <div class="param-value">Full metal frame</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Motion Structure</div>
              <div class="param-value">CoreXY</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Filament Diameter</div>
              <div class="param-value">1.75mm</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Motor Type</div>
              <div class="param-value">5:1 Dual gear reduction extruder motor</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Nozzle Material</div>
              <div class="param-value">Hardened steel</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Nozzle Size</div>
              <div class="param-value">Standard 0.4mm</div>
            </div>
            <div class="parameter-card highlight">
              <div class="param-label">Print Volume</div>
              <div class="param-value">250×250×∞mm (X*Y*Z)</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Product Dimensions</div>
              <div class="param-value">676×436×510mm</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Package Dimensions</div>
              <div class="param-value">770×510×320mm</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Net Weight</div>
              <div class="param-value">16.5kg</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Gross Weight</div>
              <div class="param-value">21kg</div>
            </div>
            <div class="parameter-card highlight">
              <div class="param-label">Print Precision</div>
              <div class="param-value">±0.1mm</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Layer Thickness</div>
              <div class="param-value">0.1-0.3mm</div>
            </div>
            <div class="parameter-card highlight">
              <div class="param-label">Print Speed</div>
              <div class="param-value">≤400mm/s</div>
            </div>
            <div class="parameter-card highlight">
              <div class="param-label">Print Acceleration</div>
              <div class="param-value">≤20000mm/s²</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Maximum Nozzle Temperature</div>
              <div class="param-value">300°C</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Maximum Heated Bed Temperature</div>
              <div class="param-value">90°C</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Nozzle Heating Time</div>
              <div class="param-value">40s</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Heated Bed Heating Time</div>
              <div class="param-value">90s</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Maximum Flow Rate</div>
              <div class="param-value">26mm³/s</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Print Platform</div>
              <div class="param-value">PEI metal build surface</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Compatible Materials</div>
              <div class="param-value">PLA/PETG/TPU/ABS/ASA, etc.</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Operating Environment Temperature</div>
              <div class="param-value">10-40°C</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Noise Level</div>
              <div class="param-value">54dB</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Print Methods</div>
              <div class="param-value">USB drive/Local network/Internet network</div>
            </div>
          </div>
        </div>

        <!-- Sensors -->
        <div class="parameter-category" data-category="sensors">
          <div class="sensor-grid">
            <div class="sensor-card">
              <div class="sensor-icon">📳</div>
              <div class="sensor-name">Vibration Compensation</div>
              <div class="sensor-status supported">✅ Supported</div>
            </div>
            <div class="sensor-card">
              <div class="sensor-icon">🧵</div>
              <div class="sensor-name">Filament Runout Detection</div>
              <div class="sensor-status supported">✅ Supported</div>
            </div>
            <div class="sensor-card">
              <div class="sensor-icon">📊</div>
              <div class="sensor-name">Material Shortage Detection</div>
              <div class="sensor-status supported">✅ Supported</div>
            </div>
            <div class="sensor-card">
              <div class="sensor-icon">🚫</div>
              <div class="sensor-name">Clogging Detection</div>
              <div class="sensor-status supported">✅ Supported</div>
            </div>
            <div class="sensor-card">
              <div class="sensor-icon">📐</div>
              <div class="sensor-name">Auto Leveling</div>
              <div class="sensor-status supported">✅ Supported</div>
            </div>
            <div class="sensor-card">
              <div class="sensor-icon">💡</div>
              <div class="sensor-name">LED Lighting</div>
              <div class="sensor-status supported">✅ Supported</div>
            </div>
            <div class="sensor-card">
              <div class="sensor-icon">📷</div>
              <div class="sensor-name">Camera</div>
              <div class="sensor-status supported">✅ Supported</div>
            </div>
          </div>
        </div>

        <!-- Electrical Hardware -->
        <div class="parameter-category" data-category="electrical">
          <div class="parameter-grid">
            <div class="parameter-card">
              <div class="param-label">Input Voltage</div>
              <div class="param-value">110VAC/220VAC, 50/60Hz</div>
            </div>
            <div class="parameter-card highlight">
              <div class="param-label">Maximum Power</div>
              <div class="param-value">800W</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Main Controller</div>
              <div class="param-value">64-bit 1.5GHz Quad-core Cortex-A53 processor</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Memory</div>
              <div class="param-value">16GB-SD, 1GB DDR3</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">User Interface</div>
              <div class="param-value">4.3-inch touchscreen with 800×480 resolution</div>
            </div>
            <div class="parameter-card highlight">
              <div class="param-label">Printing Firmware</div>
              <div class="param-value">Klipper</div>
            </div>
          </div>
        </div>

        <!-- Software -->
        <div class="parameter-category" data-category="software">
          <div class="parameter-grid">
            <div class="parameter-card">
              <div class="param-label">Slicing Software</div>
              <div class="param-value">Ideamaker/Ideaformer Cura</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Input Formats</div>
              <div class="param-value">.stl/.obj/.3mf/.step/.stp/.iges/.igs/.oltp/.jpg/.jpeg/.png/.bmp</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Operating Systems</div>
              <div class="param-value">Windows/MacOS/Linux</div>
            </div>
            <div class="parameter-card">
              <div class="param-label">Output File Format</div>
              <div class="param-value">.gcode</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

{{ 'IR3-Parameters-Display.js' | asset_url | script_tag }}

{% schema %}
{
  "name": "IR3 Parameters Display",
  "tag": "section",
  "class": "parameters-display-section",
  "settings": [
    {
      "type": "header",
      "content": "Content Settings"
    },
    {
      "type": "text",
      "id": "title",
      "label": "Section Title",
      "default": "IR3 V2 Parameters"
    },
    {
      "type": "textarea",
      "id": "description",
      "label": "Section Description",
      "default": "Comprehensive technical specifications and features overview"
    },
    {
      "type": "header",
      "content": "Spacing Settings"
    },
    {
      "type": "number",
      "id": "margin_top",
      "label": "Top Margin (px)",
      "default": 0,
      "info": "输入精确的上边距像素值"
    },
    {
      "type": "number",
      "id": "margin_bottom",
      "label": "Bottom Margin (px)",
      "default": 0,
      "info": "输入精确的下边距像素值"
    }
  ],
  "presets": [
    {
      "name": "IR3 Parameters Display"
    }
  ]
}
{% endschema %}