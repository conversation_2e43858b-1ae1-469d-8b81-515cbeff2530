/* IR3 Parameters Display Styles */

/* Base Section Styles */
.parameters-section {
  position: relative;
  min-height: 100vh;
  overflow: hidden;
  background: #000;
  color: #fff;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
}

/* Background Layer */
.parameters-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.gradient-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 25%, #0f3460 50%, #1a1a2e 75%, #000 100%);
  opacity: 0.9;
}

.tech-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(rgba(66, 165, 245, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(66, 165, 245, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: grid-move 20s linear infinite;
}

@keyframes grid-move {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

.floating-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(2px 2px at 20px 30px, rgba(29, 233, 182, 0.3), transparent),
    radial-gradient(2px 2px at 40px 70px, rgba(66, 165, 245, 0.3), transparent),
    radial-gradient(1px 1px at 90px 40px, rgba(29, 233, 182, 0.4), transparent),
    radial-gradient(1px 1px at 130px 80px, rgba(66, 165, 245, 0.4), transparent);
  background-repeat: repeat;
  background-size: 200px 100px;
  animation: particles-float 15s ease-in-out infinite;
}

@keyframes particles-float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-10px) rotate(1deg); }
  66% { transform: translateY(5px) rotate(-1deg); }
}

/* Content Container */
.parameters-container {
  position: relative;
  z-index: 2;
  padding: 2rem 0;
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1rem;
  width: 100%;
}

/* Header Section */
.parameters-header {
  text-align: center;
  margin-bottom: 3rem;
}

.section-badge {
  display: inline-block;
  padding: 0.5rem 1.5rem;
  background: linear-gradient(135deg, rgba(66, 165, 245, 0.2), rgba(29, 233, 182, 0.2));
  border: 1px solid rgba(66, 165, 245, 0.3);
  border-radius: 50px;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  color: #42a5f5;
  margin-bottom: 1rem;
  backdrop-filter: blur(10px);
}

.section-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 700;
  margin: 0 0 1rem 0;
  background: linear-gradient(135deg, #42a5f5, #1de9b6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1.2;
}

.section-description {
  font-size: 1.125rem;
  color: rgba(255, 255, 255, 0.8);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Category Navigation */
.category-nav {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
}

.category-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 50px;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.category-btn:hover {
  background: rgba(66, 165, 245, 0.1);
  border-color: rgba(66, 165, 245, 0.3);
  color: #42a5f5;
  transform: translateY(-2px);
}

.category-btn.active {
  background: linear-gradient(135deg, rgba(66, 165, 245, 0.2), rgba(29, 233, 182, 0.2));
  border-color: #42a5f5;
  color: #42a5f5;
  box-shadow: 0 4px 20px rgba(66, 165, 245, 0.3);
}

.btn-icon {
  font-size: 1rem;
}

/* Parameters Content */
.parameters-content {
  position: relative;
}

.parameter-category {
  display: none;
  animation: fadeInUp 0.6s ease-out;
}

.parameter-category.active {
  display: block;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Parameter Grid */
.parameter-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.parameter-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.parameter-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(66, 165, 245, 0.5), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.parameter-card:hover::before {
  transform: translateX(100%);
}

.parameter-card:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(66, 165, 245, 0.3);
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.parameter-card.highlight {
  background: linear-gradient(135deg, rgba(66, 165, 245, 0.1), rgba(29, 233, 182, 0.1));
  border-color: rgba(66, 165, 245, 0.3);
}

.param-label {
  font-size: 0.875rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 0.5rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.param-value {
  font-size: 1.125rem;
  color: #fff;
  font-weight: 600;
  line-height: 1.4;
}

.parameter-card.highlight .param-value {
  background: linear-gradient(135deg, #42a5f5, #1de9b6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Sensor Grid */
.sensor-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.sensor-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 2rem;
  text-align: center;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.sensor-card:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(29, 233, 182, 0.3);
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.sensor-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  display: block;
}

.sensor-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: #fff;
  margin-bottom: 0.75rem;
}

.sensor-status {
  font-size: 0.875rem;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  display: inline-block;
}

.sensor-status.supported {
  background: rgba(29, 233, 182, 0.2);
  color: #1de9b6;
  border: 1px solid rgba(29, 233, 182, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .parameters-container {
    padding: 1rem 0;
  }
  
  .container {
    padding: 0 0.75rem;
  }
  
  .parameters-header {
    margin-bottom: 2rem;
  }
  
  .section-title {
    font-size: 2.5rem;
  }
  
  .category-nav {
    gap: 0.5rem;
    margin-bottom: 2rem;
  }
  
  .category-btn {
    padding: 0.625rem 1rem;
    font-size: 0.8125rem;
  }
  
  .parameter-grid,
  .sensor-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .parameter-card,
  .sensor-card {
    padding: 1.25rem;
  }
}

@media (max-width: 480px) {
  .category-nav {
    flex-direction: column;
    align-items: center;
  }
  
  .category-btn {
    width: 100%;
    max-width: 280px;
    justify-content: center;
  }
  
  .section-description {
    font-size: 1rem;
  }
}
